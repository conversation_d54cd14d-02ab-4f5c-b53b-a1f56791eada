# JSON Schema Validation Guide

This document provides comprehensive instructions for validating JSON data files against their corresponding schemas in the Upshift project.

## Overview

The project includes JSON schema files for validating data structure consistency across different data types:

- **SystemPersona data**: `data/system_personas.json`
- **Guided Paths data**: `data/guide-paths.json`
- **UserProfile data**: User-generated profile data

## Prerequisites

### Install JSON Schema Validator

If not already installed, install the AJV CLI tool:

```bash
npm install -g ajv-cli
```

## SystemPersona Schema Validation

### Validate SystemPersona Data

To validate the `data/system_personas.json` file against its schema:

1. **Using AJV CLI**:
   ```bash
   ajv validate -s assets/schemas/system_personas.schema.json -d data/system_personas.json
   ```

2. **Using the JavaScript validation script**:
   ```bash
   node assets/schemas/system_personas_validate.js
   ```

### Expected Output

For valid JSON:
```
data/system_personas.json valid
```

Or:
```
✅ Validation passed
```

### SystemPersona Schema Requirements

Each SystemPersona object must include:

- **name** (string, required): The name of the persona
- **title** (string, required): The title or role description of the persona
- **description** (string, required): Detailed description of coaching style and approach
- **avatarUrl** (string, required): URL or path to the persona's avatar image
- **isActive** (boolean, required): Whether the persona is active and available
- **approach** (string, required): The persona's general approach to coaching
- **coachingStyle** (string, required): The specific coaching style
- **specialties** (array, required): List of specialties and areas of expertise
- **videoUrl** (string, required): URL or path to the persona's introduction video
- **catchphrase** (string, required): A memorable catchphrase or motto

## Guided Paths Schema Validation

### Validate Guided Paths Data

To validate the `data/guide-paths.json` file against its schema:

1. **Using AJV CLI**:
   ```bash
   ajv validate -s assets/schemas/guided_paths.schema.json -d data/guide-paths.json --strict=false
   ```

2. **Using the JavaScript validation script**:
   ```bash
   node assets/schemas/guided_path_validate.js
   ```

### Expected Output

For valid JSON:
```
data/guide-paths.json valid
```

## UserProfile Schema Validation

### Validate UserProfile Data

To validate UserProfile data against its schema:

```bash
ajv validate -s assets/schemas/user_profile.schema.json -d your_user_profile.json
```

## Schema Files Location

- **SystemPersona Schema**: `assets/schemas/system_personas.schema.json`
- **Guided Paths Schema**: `assets/schemas/guided_paths.schema.json`
- **UserProfile Schema**: `assets/schemas/user_profile.schema.json`

## Error Handling

### Common Validation Errors

If validation fails, the tool will show specific error messages indicating:

- **Missing required fields**: Required properties are not present
- **Invalid data types**: Values don't match expected types (string, boolean, array, etc.)
- **Enum value violations**: Values not in allowed enumeration lists
- **Schema constraint violations**: Values don't meet length, format, or other constraints
- **Additional properties**: Extra fields not defined in schema (when `additionalProperties: false`)

### Example Error Output

```json
[
  {
    "instancePath": "/0",
    "schemaPath": "#/items/additionalProperties",
    "keyword": "additionalProperties",
    "params": { "additionalProperty": "extraField" },
    "message": "must NOT have additional properties"
  }
]
```

## Automated Validation

### CI/CD Integration

For continuous integration, add schema validation to your build process:

```bash
#!/bin/bash
# Add to your CI/CD pipeline or pre-commit hooks

# Validate SystemPersona data
ajv validate -s assets/schemas/system_personas.schema.json -d data/system_personas.json
if [ $? -ne 0 ]; then
  echo "❌ SystemPersona JSON validation failed"
  exit 1
fi
echo "✅ SystemPersona JSON validation passed"

# Validate Guided Paths data
ajv validate -s assets/schemas/guided_paths.schema.json -d data/guide-paths.json
if [ $? -ne 0 ]; then
  echo "❌ Guided paths JSON validation failed"
  exit 1
fi
echo "✅ Guided paths JSON validation passed"
```

### Pre-commit Hook Example

```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "Running JSON schema validation..."

# Validate all data files
npm run validate:schemas

if [ $? -ne 0 ]; then
  echo "❌ Schema validation failed. Please fix the errors before committing."
  exit 1
fi

echo "✅ All schema validations passed"
```

## Troubleshooting

### Common Issues

1. **Missing AJV CLI**: Install with `npm install -g ajv-cli`
2. **File path errors**: Ensure you're running commands from the project root
3. **Schema not found**: Verify schema files exist in `assets/schemas/`
4. **Data file not found**: Verify data files exist in `data/` directory

### Getting Help

- Check the schema files for detailed property requirements
- Review error messages for specific validation failures
- Ensure all required fields are present and correctly typed
- Verify that file paths and references are accurate
