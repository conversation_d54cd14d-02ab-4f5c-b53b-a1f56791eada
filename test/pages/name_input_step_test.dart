import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/onboarding/name_input_step.dart';

void main() {
  group('NameInputStep Tests', () {
    testWidgets('should display correct title and subtitle', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: NameInputStep(onNameChanged: (name) {})),
      );

      // Verify the title is correct
      expect(find.text('How should we call you?'), findsOneWidget);

      // Verify the subtitle is correct
      expect(
        find.text('Your coach will use this name during conversations.'),
        findsOneWidget,
      );
    });

    testWidgets('should handle text input and call onNameChanged', (
      tester,
    ) async {
      String capturedName = '';

      await tester.pumpWidget(
        MaterialApp(
          home: NameInputStep(onNameChanged: (name) => capturedName = name),
        ),
      );

      // Find the text field and enter text
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      await tester.enterText(textField, '<PERSON>');
      await tester.pump();

      // Verify the name was captured
      expect(capturedName, equals('John'));
    });

    testWidgets('should not show preview chip', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: NameInputStep(onNameChanged: (name) {})),
      );

      // Enter a name
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'John');
      await tester.pump();

      // Verify the preview chip is NOT shown
      expect(find.text('Your coach will address you as "John"'), findsNothing);
    });

    testWidgets('should handle long names without overflow', (tester) async {
      String capturedName = '';

      await tester.pumpWidget(
        MaterialApp(
          home: NameInputStep(onNameChanged: (name) => capturedName = name),
        ),
      );

      // Enter a very long name (will be truncated to 50 chars by TextField)
      const longName = 'ThisIsAVeryLongNameThatShouldNotCauseOverflowIssues';
      final textField = find.byType(TextField);
      await tester.enterText(textField, longName);
      await tester.pump();

      // Verify the name was captured and truncated to 50 characters
      expect(capturedName.length, lessThanOrEqualTo(50));
      expect(capturedName, equals(longName.substring(0, capturedName.length)));

      // Verify no overflow errors occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('should limit input to 50 characters', (tester) async {
      String capturedName = '';

      await tester.pumpWidget(
        MaterialApp(
          home: NameInputStep(onNameChanged: (name) => capturedName = name),
        ),
      );

      // Try to enter a name longer than 50 characters
      const longName =
          'ThisIsAVeryLongNameThatIsDefinitelyOverFiftyCharactersLong';
      final textField = find.byType(TextField);
      await tester.enterText(textField, longName);
      await tester.pump();

      // Verify the input was limited to 50 characters
      expect(capturedName.length, lessThanOrEqualTo(50));

      // Verify no validation error is shown (since input is limited)
      expect(find.text('Name must be 50 characters or less'), findsNothing);
    });

    testWidgets('should be scrollable to prevent overflow', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: NameInputStep(onNameChanged: (name) {})),
      );

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('should enable continue button only when name is valid', (
      tester,
    ) async {
      String capturedName = '';

      await tester.pumpWidget(
        MaterialApp(
          home: NameInputStep(onNameChanged: (name) => capturedName = name),
        ),
      );

      // Initially, continue should be disabled (no name entered)
      // This is handled by the OnboardingStepBase widget

      // Enter a valid name
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'John');
      await tester.pump();

      // Verify the name was captured and is valid
      expect(capturedName, equals('John'));
      expect(capturedName.isNotEmpty, isTrue);
      expect(capturedName.length <= 50, isTrue);
    });
  });
}
