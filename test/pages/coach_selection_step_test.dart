import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/onboarding/coach_selection_step.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('CoachSelectionStep Tests', () {
    late List<SystemPersona> testCoaches;

    setUp(() {
      testCoaches = [
        SystemPersona(
          id: '1',
          name: 'Coach <PERSON>',
          description: 'A supportive coach.',
          coachingStyle: 'Supportive',
          approach: 'Encouraging',
          // No avatarUrl to avoid missing asset errors
        ),
        SystemPersona(
          id: '2',
          name: 'Coach <PERSON>',
          description: 'A direct coach.',
          coachingStyle: 'Direct',
          approach: 'Results-focused',
          // No avatarUrl to avoid missing asset errors
        ),
      ];
    });

    testWidgets('should display correct title and subtitle', (tester) async {
      SystemPersona? selectedCoach;

      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) => selectedCoach = coach,
            userName: '<PERSON>',
          ),
        ),
      );

      // Verify the title is correct
      expect(find.text('Choose your coach'), findsOneWidget);

      // Verify the subtitle includes the user name
      expect(
        find.text('Hi <PERSON>! Pick the coaching style that resonates with you.'),
        findsOneWidget,
      );
    });

    testWidgets('should not show page indicators (dots)', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) {},
          ),
        ),
      );

      // Verify no page indicators are shown
      expect(
        find.byType(Container).evaluate().where((element) {
          final widget = element.widget as Container;
          final decoration = widget.decoration as BoxDecoration?;
          return decoration?.shape == BoxShape.circle;
        }),
        isEmpty,
      );
    });

    testWidgets('should handle coach selection', (tester) async {
      SystemPersona? selectedCoach;

      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) => selectedCoach = coach,
          ),
        ),
      );

      // Find and tap on the first coach card
      final firstCoachCard = find.text('Coach Alex');
      expect(firstCoachCard, findsOneWidget);

      await tester.tap(firstCoachCard);
      await tester.pump();

      // Verify the coach was selected
      expect(selectedCoach, isNotNull);
      expect(selectedCoach!.id, equals('1'));
      expect(selectedCoach!.name, equals('Coach Alex'));
    });

    testWidgets('should display coaches in PageView', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) {},
          ),
        ),
      );

      // Verify PageView is present
      expect(find.byType(PageView), findsOneWidget);

      // Verify first coach is visible
      expect(find.text('Coach Alex'), findsOneWidget);

      // Swipe to see the second coach
      await tester.drag(find.byType(PageView), const Offset(-400, 0));
      await tester.pumpAndSettle();

      // Verify second coach is now visible
      expect(find.text('Coach Sarah'), findsOneWidget);
    });

    testWidgets('should handle empty coaches list', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(coaches: [], onCoachSelected: (coach) {}),
        ),
      );

      // Verify empty state message is shown
      expect(find.text('No coaches available'), findsOneWidget);
    });

    testWidgets('should use LayoutBuilder for responsive layout', (
      tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) {},
          ),
        ),
      );

      // Verify LayoutBuilder is used for responsive design
      expect(find.byType(LayoutBuilder), findsOneWidget);
    });

    testWidgets('should handle video tap callback', (tester) async {
      SystemPersona? videoTappedCoach;

      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) {},
            onVideoTap: (coach) => videoTappedCoach = coach,
          ),
        ),
      );

      // The video tap functionality would be tested by tapping on the avatar
      // This would require more complex widget testing to access the avatar specifically
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('should show continue button when coach is selected', (
      tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CoachSelectionStep(
            coaches: testCoaches,
            onCoachSelected: (coach) {},
            initialSelectedCoach: testCoaches.first,
          ),
        ),
      );

      // With a pre-selected coach, the continue functionality should be enabled
      // This is handled by the OnboardingStepBase widget
      expect(find.byType(CoachSelectionStep), findsOneWidget);
    });
  });
}
