import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/onboarding/goals_selection_step.dart';

void main() {
  group('GoalsSelectionStep Tests', () {
    testWidgets('should display correct title and subtitle', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: GoalsSelectionStep(onGoalsChanged: (goals) {})),
      );

      // Verify the title is correct
      expect(find.text('What\'s your primary goal?'), findsOneWidget);

      // Verify the subtitle is correct
      expect(
        find.text('Select all that apply. We\'ll personalize your experience.'),
        findsOneWidget,
      );
    });

    testWidgets('should display "Find My Purpose" instead of "Job Search"', (
      tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(home: GoalsSelectionStep(onGoalsChanged: (goals) {})),
      );

      // Verify "Find My Purpose" is displayed
      expect(find.text('Find My Purpose'), findsOneWidget);

      // Verify "Job Search" is NOT displayed
      expect(find.text('Job Search'), findsNothing);
      expect(find.text('Job search'), findsNothing);
    });

    testWidgets('should display all expected goals', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: GoalsSelectionStep(onGoalsChanged: (goals) {})),
      );

      // Verify all expected goals are displayed
      expect(find.text('Career growth'), findsOneWidget);
      expect(find.text('Find My Purpose'), findsOneWidget);
      expect(find.text('Work-life balance'), findsOneWidget);
      expect(find.text('Productivity'), findsOneWidget);
      expect(find.text('Confidence'), findsOneWidget);
    });

    testWidgets('should handle goal selection', (tester) async {
      List<String> capturedGoals = [];

      await tester.pumpWidget(
        MaterialApp(
          home: GoalsSelectionStep(
            onGoalsChanged: (goals) => capturedGoals = goals,
          ),
        ),
      );

      // Tap on "Find My Purpose" goal
      await tester.tap(find.text('Find My Purpose'));
      await tester.pump();

      // Verify the goal was selected
      expect(capturedGoals, contains('Find My Purpose'));
      expect(capturedGoals.length, equals(1));
    });

    testWidgets('should show selection count feedback', (tester) async {
      await tester.pumpWidget(
        MaterialApp(home: GoalsSelectionStep(onGoalsChanged: (goals) {})),
      );

      // Initially no selection count should be shown
      expect(find.text('1 goal selected'), findsNothing);

      // Tap on "Find My Purpose" goal
      await tester.tap(find.text('Find My Purpose'));
      await tester.pump();

      // Verify selection count is shown
      expect(find.text('1 goal selected'), findsOneWidget);

      // Tap on another goal
      await tester.tap(find.text('Career growth'));
      await tester.pump();

      // Verify selection count is updated
      expect(find.text('2 goals selected'), findsOneWidget);
    });

    testWidgets('should handle multiple goal selections', (tester) async {
      List<String> capturedGoals = [];

      await tester.pumpWidget(
        MaterialApp(
          home: GoalsSelectionStep(
            onGoalsChanged: (goals) => capturedGoals = goals,
          ),
        ),
      );

      // Select multiple goals (using visible ones)
      await tester.tap(find.text('Find My Purpose'));
      await tester.pump();
      await tester.tap(find.text('Career growth'));
      await tester.pump();
      await tester.tap(find.text('Work-life balance'));
      await tester.pump();

      // Verify all goals were selected
      expect(capturedGoals, contains('Find My Purpose'));
      expect(capturedGoals, contains('Career growth'));
      expect(capturedGoals, contains('Work-life balance'));
      expect(capturedGoals.length, equals(3));
    });

    testWidgets('should handle goal deselection', (tester) async {
      List<String> capturedGoals = [];

      await tester.pumpWidget(
        MaterialApp(
          home: GoalsSelectionStep(
            onGoalsChanged: (goals) => capturedGoals = goals,
          ),
        ),
      );

      // Select a goal
      await tester.tap(find.text('Find My Purpose'));
      await tester.pump();
      expect(capturedGoals, contains('Find My Purpose'));

      // Deselect the same goal
      await tester.tap(find.text('Find My Purpose'));
      await tester.pump();

      // Verify the goal was deselected
      expect(capturedGoals, isNot(contains('Find My Purpose')));
      expect(capturedGoals.length, equals(0));
    });
  });
}
