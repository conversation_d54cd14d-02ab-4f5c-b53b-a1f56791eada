import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:upshift/services/in_app_rating_service.dart';

void main() {
  group('InAppRatingService', () {
    late InAppRatingService service;

    setUp(() async {
      // Clear shared preferences before each test
      SharedPreferences.setMockInitialValues({});
      service = InAppRatingService.instance;
      await service.initialize();
      // Reset tracking data to ensure clean state
      await service.resetTracking();
    });

    test('should initialize successfully', () {
      expect(service, isNotNull);
    });

    test('should track path completion count', () async {
      // Get initial statistics
      var stats = service.getStatistics();
      expect(stats['completionCount'], 0);

      // Simulate path completion
      await service.requestReviewIfNeeded();

      // Check updated statistics
      stats = service.getStatistics();
      expect(stats['completionCount'], 1);
    });

    test('should respect completion thresholds', () async {
      var stats = service.getStatistics();

      // Should not show review before threshold
      expect(stats['completionCount'], 0);
      expect(stats['requestCount'], 0);
      expect(stats['nextThreshold'], 3);
    });

    test('should reset tracking data', () async {
      // Add some completion data
      await service.requestReviewIfNeeded();
      await service.requestReviewIfNeeded();

      var stats = service.getStatistics();
      expect(stats['completionCount'], 2);

      // Reset tracking
      await service.resetTracking();

      // Verify reset
      stats = service.getStatistics();
      expect(stats['completionCount'], 0);
      expect(stats['requestCount'], 0);
    });

    test('should handle feature enabled/disabled state', () {
      var stats = service.getStatistics();
      // Default should be enabled when Remote Config is unavailable
      expect(stats['featureEnabled'], true);
    });
  });
}
