import 'package:flutter/foundation.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'logging_service.dart';
import 'remote_config_service.dart';
import 'analytics_service.dart';

/// Service for managing in-app review prompts at strategic moments
///
/// This service handles:
/// - In-app review prompt timing and frequency control
/// - Firebase Remote Config integration for feature toggling
/// - Local storage for tracking review request history
/// - Rate limiting to avoid showing prompts too frequently
/// - Analytics tracking for review prompt events
/// - Error handling and logging for review operations
class InAppRatingService {
  static const String _logTag = 'InAppRatingService';
  static InAppRatingService? _instance;

  bool _isInitialized = false;
  SharedPreferences? _prefs;
  InAppReview? _inAppReview;

  // SharedPreferences keys
  static const String _reviewRequestCountKey = 'in_app_review_request_count';
  static const String _lastReviewRequestDateKey = 'last_review_request_date';
  static const String _pathCompletionCountKey = 'path_completion_count';

  // Remote Config key
  static const String _showRatingOnPathCompletionKey =
      'show_rating_on_path_completion';

  // Rate limiting constants (following iOS App Store guidelines)
  static const int _maxReviewRequestsPerYear = 3;
  static const int _minDaysBetweenRequests = 30;

  // Completion thresholds (exponential backoff)
  static const List<int> _completionThresholds = [1, 3, 8];

  /// Private constructor for singleton pattern
  InAppRatingService._();

  /// Get the singleton instance
  static InAppRatingService get instance {
    _instance ??= InAppRatingService._();
    return _instance!;
  }

  /// Initialize the In-App Rating service
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Initializing InAppRatingService',
      );

      _prefs = await SharedPreferences.getInstance();
      _inAppReview = InAppReview.instance;

      _isInitialized = true;

      LoggingService.instance.logInfo(
        _logTag,
        'InAppRatingService initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to initialize InAppRatingService',
      );
      // Don't rethrow - allow app to continue without review prompts
    }
  }

  /// Ensure the service is initialized before use
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'InAppRatingService must be initialized before use. '
        'Call initialize() first.',
      );
    }
  }

  /// Request a review if certain conditions are met
  /// This is the main method to call when a user completes a guided path
  Future<void> requestReviewIfNeeded() async {
    try {
      _ensureInitialized();

      // Don't show rating prompt on web (not supported)
      if (kIsWeb) {
        LoggingService.instance.logInfo(
          _logTag,
          'Skipping review request on web platform',
        );
        return;
      }

      // Check if feature is enabled via Remote Config
      if (!_isFeatureEnabled()) {
        LoggingService.instance.logInfo(
          _logTag,
          'Review prompts disabled via Remote Config',
        );
        return;
      }

      // Increment path completion count
      await _incrementPathCompletionCount();

      // Check if we should show the review prompt
      if (await _shouldShowReviewPrompt()) {
        await _showReviewPrompt();
      }
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to request review',
      );
      // Don't rethrow - review prompts should never crash the app
    }
  }

  /// Check if the review feature is enabled via Remote Config
  bool _isFeatureEnabled() {
    try {
      final configValues = RemoteConfigService.instance.getAllConfigValues();
      final isEnabled = configValues[_showRatingOnPathCompletionKey];

      // Default to true if Remote Config is unavailable
      if (isEnabled == null || isEnabled.isEmpty) {
        return true;
      }

      return isEnabled.toLowerCase() == 'true';
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Failed to get Remote Config value, defaulting to enabled: $e',
      );
      return true; // Default to enabled if Remote Config fails
    }
  }

  /// Increment the path completion count
  Future<void> _incrementPathCompletionCount() async {
    final currentCount = _prefs!.getInt(_pathCompletionCountKey) ?? 0;
    await _prefs!.setInt(_pathCompletionCountKey, currentCount + 1);

    LoggingService.instance.logInfo(
      _logTag,
      'Path completion count incremented to ${currentCount + 1}',
    );
  }

  /// Check if we should show the review prompt based on various conditions
  Future<bool> _shouldShowReviewPrompt() async {
    final completionCount = _prefs!.getInt(_pathCompletionCountKey) ?? 0;
    final requestCount = _prefs!.getInt(_reviewRequestCountKey) ?? 0;
    final lastRequestDate = _prefs!.getString(_lastReviewRequestDateKey);

    // Check if we've reached the maximum number of requests
    if (requestCount >= _maxReviewRequestsPerYear) {
      LoggingService.instance.logInfo(
        _logTag,
        'Maximum review requests reached for this year ($requestCount)',
      );
      return false;
    }

    // Check if enough time has passed since the last request
    if (lastRequestDate != null) {
      final lastRequest = DateTime.tryParse(lastRequestDate);
      if (lastRequest != null) {
        final daysSinceLastRequest = DateTime.now()
            .difference(lastRequest)
            .inDays;
        if (daysSinceLastRequest < _minDaysBetweenRequests) {
          LoggingService.instance.logInfo(
            _logTag,
            'Not enough time since last request ($daysSinceLastRequest days)',
          );
          return false;
        }
      }
    }

    // Check if we've reached a completion threshold
    final thresholdIndex = requestCount < _completionThresholds.length
        ? requestCount
        : _completionThresholds.length - 1;
    final threshold = _completionThresholds[thresholdIndex];

    if (completionCount >= threshold) {
      LoggingService.instance.logInfo(
        _logTag,
        'Review threshold met: $completionCount >= $threshold '
        '(request #${requestCount + 1})',
      );
      return true;
    }

    LoggingService.instance.logInfo(
      _logTag,
      'Review threshold not met: $completionCount < $threshold',
    );
    return false;
  }

  /// Show the actual review prompt
  Future<void> _showReviewPrompt() async {
    try {
      // Check if the review dialog is available
      if (await _inAppReview!.isAvailable()) {
        LoggingService.instance.logInfo(
          _logTag,
          'Showing in-app review prompt',
        );

        // Request the review
        await _inAppReview!.requestReview();

        // Update tracking data
        await _updateRequestTracking();

        // Log analytics event
        await AnalyticsService.instance.logEvent(
          name: 'in_app_review_prompted',
          parameters: {
            'completion_count': _prefs!.getInt(_pathCompletionCountKey) ?? 0,
            'request_count': _prefs!.getInt(_reviewRequestCountKey) ?? 0,
          },
        );

        LoggingService.instance.logInfo(
          _logTag,
          'In-app review prompt shown successfully',
        );
      } else {
        LoggingService.instance.logInfo(
          _logTag,
          'In-app review not available on this device',
        );
      }
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to show review prompt',
      );
    }
  }

  /// Update tracking data after showing a review prompt
  Future<void> _updateRequestTracking() async {
    final currentCount = _prefs!.getInt(_reviewRequestCountKey) ?? 0;
    await _prefs!.setInt(_reviewRequestCountKey, currentCount + 1);
    await _prefs!.setString(
      _lastReviewRequestDateKey,
      DateTime.now().toIso8601String(),
    );
  }

  /// Get current statistics for debugging/testing
  Map<String, dynamic> getStatistics() {
    try {
      _ensureInitialized();

      return {
        'completionCount': _prefs!.getInt(_pathCompletionCountKey) ?? 0,
        'requestCount': _prefs!.getInt(_reviewRequestCountKey) ?? 0,
        'lastRequestDate': _prefs!.getString(_lastReviewRequestDateKey),
        'featureEnabled': _isFeatureEnabled(),
        'nextThreshold': _getNextThreshold(),
      };
    } catch (e) {
      LoggingService.instance.logInfo(_logTag, 'Error getting statistics: $e');
      return {};
    }
  }

  /// Get the next completion threshold for the current request count
  int _getNextThreshold() {
    final requestCount = _prefs!.getInt(_reviewRequestCountKey) ?? 0;
    if (requestCount < _completionThresholds.length) {
      return _completionThresholds[requestCount];
    }
    return _completionThresholds.last;
  }

  /// Reset all tracking data (useful for testing)
  Future<void> resetTracking() async {
    try {
      _ensureInitialized();

      await _prefs!.remove(_reviewRequestCountKey);
      await _prefs!.remove(_lastReviewRequestDateKey);
      await _prefs!.remove(_pathCompletionCountKey);

      LoggingService.instance.logInfo(_logTag, 'Review tracking data reset');
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to reset tracking data',
      );
    }
  }
}
