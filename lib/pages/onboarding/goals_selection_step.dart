import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/onboarding/coach_chip.dart';

/// Step 2: Goals Selection
/// User selects their primary goals (multi-select)
class GoalsSelectionStep extends StatefulWidget {
  final Function(List<String>) onGoalsChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final List<String> initialGoals;

  const GoalsSelectionStep({
    super.key,
    required this.onGoalsChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialGoals = const [],
  });

  @override
  State<GoalsSelectionStep> createState() => _GoalsSelectionStepState();
}

class _GoalsSelectionStepState extends State<GoalsSelectionStep> {
  List<String> _selectedGoals = [];

  static const List<String> _availableGoals = [
    'Career growth',
    'Find My Purpose',
    'Work-life balance',
    'Productivity',
    'Confidence',
  ];

  @override
  void initState() {
    super.initState();
    _selectedGoals = List.from(widget.initialGoals);
  }

  void _onGoalToggled(String goal) {
    setState(() {
      if (_selectedGoals.contains(goal)) {
        _selectedGoals.remove(goal);
      } else {
        _selectedGoals.add(goal);
      }
    });
    widget.onGoalsChanged(_selectedGoals);
  }

  bool get _canContinue => _selectedGoals.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: 2,
      totalSteps: 5,
      title: 'What\'s your primary goal?',
      subtitle: 'Select all that apply. We\'ll personalize your experience.',
      onBack: widget.onBack,
      onSkip: widget.onSkip,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: Column(
        children: [
          // Goals list
          Expanded(
            child: ListView.builder(
              itemCount: _availableGoals.length,
              itemBuilder: (context, index) {
                final goal = _availableGoals[index];
                final isSelected = _selectedGoals.contains(goal);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: CoachChip(
                    text: goal,
                    isSelected: isSelected,
                    fullWidth: true,
                    onTap: () => _onGoalToggled(goal),
                  ),
                );
              },
            ),
          ),

          // Selection count feedback
          if (_selectedGoals.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Text(
                '${_selectedGoals.length} goal${_selectedGoals.length == 1 ? '' : 's'} selected',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
