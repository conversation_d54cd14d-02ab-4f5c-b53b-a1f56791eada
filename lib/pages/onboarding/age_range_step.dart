import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/onboarding/coach_chip.dart';

/// Step 4: Age Range
/// User selects their age range to help tailor advice (single-select)
class AgeRangeStep extends StatefulWidget {
  final Function(String?) onAgeRangeChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final String? initialAgeRange;

  const AgeRangeStep({
    super.key,
    required this.onAgeRangeChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialAgeRange,
  });

  @override
  State<AgeRangeStep> createState() => _AgeRangeStepState();
}

class _AgeRangeStepState extends State<AgeRangeStep> {
  String? _selectedAgeRange;

  static const List<String> _ageRanges = [
    '18–24',
    '25–34',
    '35–44',
    '45–54',
    '55+',
  ];

  @override
  void initState() {
    super.initState();
    _selectedAgeRange = widget.initialAgeRange;
  }

  void _onAgeRangeSelected(String ageRange) {
    setState(() {
      if (_selectedAgeRange == ageRange) {
        _selectedAgeRange = null; // Allow deselection
      } else {
        _selectedAgeRange = ageRange;
      }
    });
    widget.onAgeRangeChanged(_selectedAgeRange);
  }

  bool get _canContinue => _selectedAgeRange != null;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: 4,
      totalSteps: 5,
      title: 'Age range',
      subtitle: 'This helps us tailor advice to your career stage.',
      onBack: widget.onBack,
      onSkip: widget.onSkip,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: Column(
        children: [
          // Age range options list
          Expanded(
            child: ListView.builder(
              itemCount: _ageRanges.length,
              itemBuilder: (context, index) {
                final ageRange = _ageRanges[index];
                final isSelected = _selectedAgeRange == ageRange;
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: CoachChip(
                    text: ageRange,
                    isSelected: isSelected,
                    fullWidth: true,
                    onTap: () => _onAgeRangeSelected(ageRange),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
