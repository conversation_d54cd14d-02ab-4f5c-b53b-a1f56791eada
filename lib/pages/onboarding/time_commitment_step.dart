import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/onboarding/coach_chip.dart';

/// Step 3: Time Commitment
/// User selects how much time they can dedicate per session (single-select)
class TimeCommitmentStep extends StatefulWidget {
  final Function(String?) onTimeCommitmentChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final String? initialTimeCommitment;

  const TimeCommitmentStep({
    super.key,
    required this.onTimeCommitmentChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialTimeCommitment,
  });

  @override
  State<TimeCommitmentStep> createState() => _TimeCommitmentStepState();
}

class _TimeCommitmentStepState extends State<TimeCommitmentStep> {
  String? _selectedTimeCommitment;

  static const List<String> _timeOptions = [
    '10–20 minutes',
    '30–45 minutes',
    '1–2 hours',
    '3+ hours',
  ];

  @override
  void initState() {
    super.initState();
    _selectedTimeCommitment = widget.initialTimeCommitment;
  }

  void _onTimeCommitmentSelected(String timeCommitment) {
    setState(() {
      if (_selectedTimeCommitment == timeCommitment) {
        _selectedTimeCommitment = null; // Allow deselection
      } else {
        _selectedTimeCommitment = timeCommitment;
      }
    });
    widget.onTimeCommitmentChanged(_selectedTimeCommitment);
  }

  bool get _canContinue => _selectedTimeCommitment != null;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: 3,
      totalSteps: 5,
      title: 'Time commitment',
      subtitle: 'How much time can you dedicate per session?',
      onBack: widget.onBack,
      onSkip: widget.onSkip,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: Column(
        children: [
          // Time options list
          Expanded(
            child: ListView.builder(
              itemCount: _timeOptions.length,
              itemBuilder: (context, index) {
                final timeOption = _timeOptions[index];
                final isSelected = _selectedTimeCommitment == timeOption;
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: CoachChip(
                    text: timeOption,
                    isSelected: isSelected,
                    fullWidth: true,
                    onTap: () => _onTimeCommitmentSelected(timeOption),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
