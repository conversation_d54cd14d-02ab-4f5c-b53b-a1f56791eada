import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/models.dart';
import '../../services/firestore.dart';
import '../../services/analytics_service.dart';
import '../../services/performance_service.dart';
import '../../services/logging_service.dart';
import '../../theme/theme.dart';
import '../../widgets/persona_video_modal.dart';
import '../main_navigation.dart';
import 'name_input_step.dart';
import 'goals_selection_step.dart';
import 'time_commitment_step.dart';
import 'age_range_step.dart';
import 'coach_selection_step.dart';

/// Main onboarding flow controller that manages the 5-step process
class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> {
  int _currentStep = 0;
  bool _isLoading = false;

  // Onboarding data
  String _userName = '';
  List<String> _selectedGoals = [];
  String? _timeCommitment;
  String? _ageRange;
  SystemPersona? _selectedCoach;

  // System personas for coach selection
  List<SystemPersona> _systemPersonas = [];
  bool _isLoadingPersonas = false;

  @override
  void initState() {
    super.initState();
    _loadSystemPersonas();
  }

  Future<void> _loadSystemPersonas() async {
    setState(() {
      _isLoadingPersonas = true;
    });

    try {
      final personas = await FirestoreService.getActiveSystemPersonas();
      setState(() {
        _systemPersonas = personas;
        _isLoadingPersonas = false;
      });
    } catch (e) {
      LoggingService.instance.logError(
        e,
        StackTrace.current,
        'OnboardingFlow',
        'Failed to load system personas',
      );
      setState(() {
        _isLoadingPersonas = false;
      });
    }
  }

  void _nextStep() {
    if (_currentStep < 4) {
      setState(() {
        _currentStep++;
      });
    } else {
      _completeOnboarding();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  void _skipStep() {
    _nextStep();
  }

  Future<void> _completeOnboarding() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Measure onboarding completion performance
      await PerformanceService.instance.measureOnboardingCompletion(() async {
        await FirestoreService.updateUserOnboarding(
          userId: currentUser.uid,
          name: _userName,
          description: null, // Not collected in this flow
          preferredPersonaIds: _selectedCoach != null
              ? [_selectedCoach!.id ?? '']
              : [],
        );

        // Track onboarding completion in analytics
        await AnalyticsService.instance.logOnboardingComplete();

        // Update user properties
        await AnalyticsService.instance.setUserProperty(
          name: 'is_onboarded',
          value: 'true',
        );

        if (_selectedCoach != null) {
          await AnalyticsService.instance.logPersonaSelected(
            personaId: _selectedCoach!.id ?? '',
            selectionContext: 'onboarding',
          );
        }
      }, _selectedGoals.length);

      if (mounted) {
        // Navigate to main app
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationPage()),
        );
      }
    } catch (e) {
      LoggingService.instance.logError(
        e,
        StackTrace.current,
        'OnboardingFlow',
        'Failed to complete onboarding',
      );
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to complete onboarding: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: AppColors.bg0,
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
          ),
        ),
      );
    }

    switch (_currentStep) {
      case 0:
        return NameInputStep(
          onNameChanged: (name) => _userName = name,
          onBack: _currentStep > 0 ? _previousStep : null,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialName: _userName,
        );

      case 1:
        return GoalsSelectionStep(
          onGoalsChanged: (goals) => _selectedGoals = goals,
          onBack: _previousStep,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialGoals: _selectedGoals,
        );

      case 2:
        return TimeCommitmentStep(
          onTimeCommitmentChanged: (timeCommitment) =>
              _timeCommitment = timeCommitment,
          onBack: _previousStep,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialTimeCommitment: _timeCommitment,
        );

      case 3:
        return AgeRangeStep(
          onAgeRangeChanged: (ageRange) => _ageRange = ageRange,
          onBack: _previousStep,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialAgeRange: _ageRange,
        );

      case 4:
        return CoachSelectionStep(
          coaches: _systemPersonas,
          onCoachSelected: (coach) => _selectedCoach = coach,
          onVideoTap: (coach) {
            LoggingService.instance.logInfo(
              'OnboardingFlow',
              'Video tap for coach: ${coach.name}',
            );
            // Show video modal for the selected coach
            PersonaVideoModal.show(context, persona: coach);
          },
          onBack: _previousStep,
          onContinue: _nextStep,
          initialSelectedCoach: _selectedCoach,
          userName: _userName,
        );

      default:
        return NameInputStep(
          onNameChanged: (name) => _userName = name,
          onBack: null,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialName: _userName,
        );
    }
  }
}
