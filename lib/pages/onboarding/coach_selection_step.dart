import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../../models/models.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/onboarding/coach_card.dart';

/// Step 5: Coach Selection
/// User selects their preferred coach from a horizontal carousel
class CoachSelectionStep extends StatefulWidget {
  final List<SystemPersona> coaches;
  final Function(SystemPersona?) onCoachSelected;
  final Function(SystemPersona)? onVideoTap;
  final VoidCallback? onBack;
  final VoidCallback? onContinue;
  final SystemPersona? initialSelectedCoach;
  final String userName;

  const CoachSelectionStep({
    super.key,
    required this.coaches,
    required this.onCoachSelected,
    this.onVideoTap,
    this.onBack,
    this.onContinue,
    this.initialSelectedCoach,
    this.userName = '',
  });

  @override
  State<CoachSelectionStep> createState() => _CoachSelectionStepState();
}

class _CoachSelectionStepState extends State<CoachSelectionStep> {
  SystemPersona? _selectedCoach;
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _selectedCoach = widget.initialSelectedCoach;

    // Find initial index if coach is pre-selected
    if (_selectedCoach != null) {
      _currentIndex = widget.coaches.indexWhere(
        (coach) => coach.id == _selectedCoach!.id,
      );
      if (_currentIndex == -1) _currentIndex = 0;
    }

    _pageController = PageController(
      initialPage: _currentIndex,
      viewportFraction: 0.8, // Show part of adjacent cards
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onCoachSelected(SystemPersona coach) {
    setState(() {
      _selectedCoach = coach;
    });
    widget.onCoachSelected(coach);
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  bool get _canContinue => _selectedCoach != null;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: 5,
      totalSteps: 5,
      title: 'Choose your coach',
      subtitle: widget.userName.isNotEmpty
          ? 'Hi ${widget.userName}! Pick the coaching style that resonates with you.'
          : 'Pick the coaching style that resonates with you.',
      onBack: widget.onBack,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              // Coach carousel - use available height minus padding
              SizedBox(
                height: constraints.maxHeight,
                child: widget.coaches.isEmpty
                    ? const Center(
                        child: Text(
                          'No coaches available',
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 16,
                          ),
                        ),
                      )
                    : PageView.builder(
                        controller: _pageController,
                        onPageChanged: _onPageChanged,
                        itemCount: widget.coaches.length,
                        itemBuilder: (context, index) {
                          final coach = widget.coaches[index];
                          final isSelected = _selectedCoach?.id == coach.id;

                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: OnboardingCoachCard(
                              persona: coach,
                              isSelected: isSelected,
                              onTap: () => _onCoachSelected(coach),
                              onPlayTap: widget.onVideoTap != null
                                  ? () => widget.onVideoTap!(coach)
                                  : null,
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }
}
