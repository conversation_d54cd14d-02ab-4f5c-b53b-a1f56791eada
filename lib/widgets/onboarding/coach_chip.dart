import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// A reusable chip widget for the onboarding flow
/// Supports selection (toggleable) and tag (display-only) variants
class CoachChip extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback? onTap;
  final CoachChipVariant variant;
  final bool fullWidth;

  const CoachChip({
    super.key,
    required this.text,
    this.isSelected = false,
    this.onTap,
    this.variant = CoachChipVariant.selection,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget chip = GestureDetector(
      onTap: variant == CoachChipVariant.selection ? onTap : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: _getPadding(),
        decoration: _getDecoration(),
        child: Text(text, style: _getTextStyle(), textAlign: TextAlign.center),
      ),
    );

    if (fullWidth) {
      return SizedBox(width: double.infinity, child: chip);
    }

    return chip;
  }

  EdgeInsetsGeometry _getPadding() {
    switch (variant) {
      case CoachChipVariant.selection:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
      case CoachChipVariant.tag:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
    }
  }

  BoxDecoration _getDecoration() {
    switch (variant) {
      case CoachChipVariant.selection:
        if (isSelected) {
          return BoxDecoration(
            color: AppColors.chipSelection,
            borderRadius: BorderRadius.circular(999), // Pill shape
            // Remove border for selected state to avoid double border effect
          );
        } else {
          return BoxDecoration(
            color: AppColors.bg2,
            borderRadius: BorderRadius.circular(999), // Pill shape
            border: Border.all(color: AppColors.line, width: 1),
          );
        }

      case CoachChipVariant.tag:
        return BoxDecoration(
          color: AppColors.bg2,
          borderRadius: BorderRadius.circular(999), // Pill shape
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (variant) {
      case CoachChipVariant.selection:
        return TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          height: 1.4,
          color: isSelected ? Colors.black : AppColors.textPrimary,
        );

      case CoachChipVariant.tag:
        return const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          height: 1.4,
          color: AppColors.textSecondary,
        );
    }
  }
}

enum CoachChipVariant { selection, tag }

/// A widget that displays multiple selection chips in a vertical layout
class CoachChipGroup extends StatelessWidget {
  final List<String> options;
  final List<String> selectedOptions;
  final Function(List<String>) onSelectionChanged;
  final bool multiSelect;
  final int? maxSelections;

  const CoachChipGroup({
    super.key,
    required this.options,
    required this.selectedOptions,
    required this.onSelectionChanged,
    this.multiSelect = true,
    this.maxSelections,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: options.map((option) {
        final isSelected = selectedOptions.contains(option);

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CoachChip(
            text: option,
            isSelected: isSelected,
            fullWidth: true,
            onTap: () => _handleSelection(option),
          ),
        );
      }).toList(),
    );
  }

  void _handleSelection(String option) {
    List<String> newSelection = List.from(selectedOptions);

    if (multiSelect) {
      if (newSelection.contains(option)) {
        newSelection.remove(option);
      } else {
        // Check max selections limit
        if (maxSelections != null && newSelection.length >= maxSelections!) {
          return; // Don't allow more selections
        }
        newSelection.add(option);
      }
    } else {
      // Single select
      if (newSelection.contains(option)) {
        newSelection.clear(); // Allow deselection
      } else {
        newSelection = [option];
      }
    }

    onSelectionChanged(newSelection);
  }
}
