import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../../models/models.dart';

/// A coach card widget that uses the original PersonaCard design
/// Restored to match the previous design before Figma changes
class OnboardingCoachCard extends StatelessWidget {
  final SystemPersona persona;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onPlayTap;
  final double? width;
  final double? height;

  const OnboardingCoachCard({
    super.key,
    required this.persona,
    this.isSelected = false,
    this.onTap,
    this.onPlayTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Card(
        elevation: AppDimensions.elevationS,
        child: InkWell(
          onTap: onTap,
          borderRadius: AppDimensions.borderRadiusM,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: AppDimensions.borderRadiusM,
              border: isSelected
                  ? Border.all(color: context.colorScheme.primary, width: 2)
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Avatar section - flexible height that adapts to available space
                Expanded(
                  flex: 3, // Takes 3/4 of available space
                  child: _buildAvatar(context),
                ),

                // Content section - fixed height to prevent overflow
                Container(
                  height: 120, // Fixed height to prevent overflow
                  padding: const EdgeInsets.all(AppDimensions.spacingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name - single line
                      Text(
                        persona.name,
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppDimensions.spacingXs),

                      // Tags - single line with horizontal scroll
                      if (persona.coachingStyle != null ||
                          persona.approach != null)
                        SizedBox(
                          height: 24,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                if (persona.coachingStyle != null)
                                  _buildTag(context, persona.coachingStyle!),
                                if (persona.coachingStyle != null &&
                                    persona.approach != null)
                                  const SizedBox(
                                    width: AppDimensions.spacingXs,
                                  ),
                                if (persona.approach != null)
                                  _buildTag(context, persona.approach!),
                              ],
                            ),
                          ),
                        ),

                      const SizedBox(height: AppDimensions.spacingS),

                      // Description - remaining space
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            persona.description ?? '',
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return ClipRRect(
      borderRadius: AppDimensions.borderRadiusL,
      child: GestureDetector(
        onTap: onPlayTap,
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                borderRadius: AppDimensions.borderRadiusL,
                color: context.colorScheme.primaryContainer,
                image: _getCoachImage() != null
                    ? DecorationImage(
                        image: _getCoachImage()!,
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: _getCoachImage() == null
                  ? Icon(
                      AppIcons.profile,
                      size: 60,
                      color: context.colorScheme.onPrimaryContainer,
                    )
                  : null,
            ),
            // Play button overlay
            if (onPlayTap != null)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: AppDimensions.borderRadiusL,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 40,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXs,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: AppDimensions.borderRadiusS,
      ),
      child: Text(
        text,
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colorScheme.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  ImageProvider? _getCoachImage() {
    if (persona.avatarUrl != null && persona.avatarUrl!.isNotEmpty) {
      // Check if it's an asset path or network URL
      if (persona.avatarUrl!.startsWith('assets/')) {
        return AssetImage(persona.avatarUrl!);
      } else if (persona.avatarUrl!.startsWith('http://') ||
          persona.avatarUrl!.startsWith('https://')) {
        return NetworkImage(persona.avatarUrl!);
      } else {
        // Assume it's an asset if it doesn't start with http
        return AssetImage(persona.avatarUrl!);
      }
    }
    return null;
  }
}
