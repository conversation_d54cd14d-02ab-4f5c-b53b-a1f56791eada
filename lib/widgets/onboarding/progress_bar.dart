import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// A progress bar widget for the onboarding flow
/// Shows current step / total steps with yellow accent fill
class ProgressBar extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final double height;

  const ProgressBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.height = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    final progress = currentStep / totalSteps;
    
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: AppColors.line,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.accent,
            borderRadius: BorderRadius.circular(height / 2),
          ),
        ),
      ),
    );
  }
}

/// A progress bar with step indicators
class SteppedProgressBar extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final double height;
  final double stepIndicatorSize;

  const SteppedProgressBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.height = 4.0,
    this.stepIndicatorSize = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: stepIndicatorSize,
      child: Row(
        children: List.generate(totalSteps, (index) {
          final stepNumber = index + 1;
          final isCompleted = stepNumber <= currentStep;
          final isCurrent = stepNumber == currentStep;
          
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                right: index < totalSteps - 1 ? 4 : 0,
              ),
              height: height,
              decoration: BoxDecoration(
                color: isCompleted ? AppColors.accent : AppColors.line,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
