import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

/// Subscription entitlements for the Upshift app
///
/// Defines the different subscription tiers and their access levels:
/// - No entitlement = Free tier (access to one starter path per category)
/// - Premium entitlement = Premium tier (unlimited access to all guided paths)
enum SubscriptionEntitlement {
  @JsonValue('premium')
  premium,
}

/// Extension methods for SubscriptionEntitlement
extension SubscriptionEntitlementExtension on SubscriptionEntitlement {
  /// Get the string identifier for this entitlement
  String get identifier {
    switch (this) {
      case SubscriptionEntitlement.premium:
        return 'premium';
    }
  }

  /// Get the display name for this entitlement
  String get displayName {
    switch (this) {
      case SubscriptionEntitlement.premium:
        return 'Premium';
    }
  }

  /// Check if this entitlement provides premium access
  bool get isPremium {
    switch (this) {
      case SubscriptionEntitlement.premium:
        return true;
    }
  }
}

/// User subscription status and entitlements
@JsonSerializable()
class UserSubscription {
  final bool isActive;
  final List<SubscriptionEntitlement> entitlements;
  final String? subscriptionType; // 'monthly', 'annual', etc.
  final DateTime? expirationDate;
  final DateTime? purchaseDate;
  final String? originalTransactionId;
  final bool willRenew;

  @TimestampDateTimeConverter()
  final DateTime lastChecked;

  const UserSubscription({
    this.isActive = false,
    this.entitlements = const [],
    this.subscriptionType,
    this.expirationDate,
    this.purchaseDate,
    this.originalTransactionId,
    this.willRenew = false,
    required this.lastChecked,
  });

  /// Factory constructor from JSON
  factory UserSubscription.fromJson(Map<String, dynamic> json) =>
      _$UserSubscriptionFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserSubscriptionToJson(this);

  /// Check if user has premium access
  bool get hasPremiumAccess {
    return isActive && entitlements.any((e) => e.isPremium);
  }

  /// Check if user has specific entitlement
  bool hasEntitlement(SubscriptionEntitlement entitlement) {
    return isActive && entitlements.contains(entitlement);
  }

  /// Get user tier based on entitlements
  String get userTier {
    return hasPremiumAccess ? 'paid' : 'free';
  }

  /// Copy with method for updating subscription data
  UserSubscription copyWith({
    bool? isActive,
    List<SubscriptionEntitlement>? entitlements,
    String? subscriptionType,
    DateTime? expirationDate,
    DateTime? purchaseDate,
    String? originalTransactionId,
    bool? willRenew,
    DateTime? lastChecked,
  }) {
    return UserSubscription(
      isActive: isActive ?? this.isActive,
      entitlements: entitlements ?? this.entitlements,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      expirationDate: expirationDate ?? this.expirationDate,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      originalTransactionId:
          originalTransactionId ?? this.originalTransactionId,
      willRenew: willRenew ?? this.willRenew,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }

  /// Create a default free subscription
  factory UserSubscription.free() {
    return UserSubscription(lastChecked: DateTime.now());
  }
}

class TimestampDateTimeConverter implements JsonConverter<DateTime, dynamic> {
  const TimestampDateTimeConverter();

  @override
  DateTime fromJson(dynamic json) {
    if (json == null) {
      throw Exception('Cannot convert null to DateTime');
    }
    if (json is Timestamp) return json.toDate();
    if (json is String) {
      final parsed = DateTime.tryParse(json);
      if (parsed == null) {
        throw Exception('Cannot parse string "$json" to DateTime');
      }
      return parsed;
    }
    throw Exception('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime object) {
    return Timestamp.fromDate(object);
  }
}

class NullableTimestampDateTimeConverter
    implements JsonConverter<DateTime?, dynamic> {
  const NullableTimestampDateTimeConverter();

  @override
  DateTime? fromJson(dynamic json) {
    if (json == null) {
      return null;
    }
    if (json is Timestamp) return json.toDate();
    if (json is String) {
      final parsed = DateTime.tryParse(json);
      if (parsed == null) {
        throw Exception('Cannot parse string "$json" to DateTime');
      }
      return parsed;
    }
    throw Exception('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime? object) {
    if (object == null) return null;
    return Timestamp.fromDate(object);
  }
}

/// Notification preferences for push notifications and email notifications
@JsonSerializable()
class NotificationPreferences {
  /// Master toggle for all push notifications
  final bool enablePushNotifications;

  /// Toggle for email notifications
  final bool enableEmailNotifications;

  /// Granular control for specific notification topics/channels
  final Map<String, bool> topicPreferences;

  /// FCM tokens for push notifications (Map&lt;deviceId, fcmToken&gt;)
  /// Supports multiple devices per user
  final Map<String, String> fcmTokens;

  const NotificationPreferences({
    this.enablePushNotifications = true,
    this.enableEmailNotifications = true,
    this.topicPreferences = const {},
    this.fcmTokens = const {},
  });

  /// Create default notification preferences
  factory NotificationPreferences.defaultPreferences() {
    return const NotificationPreferences(
      enablePushNotifications: true,
      enableEmailNotifications: true,
      topicPreferences: {
        'goals_and_progress': true,
        'guided_path_updates': true,
        'system_announcements': true,
        'weekly_insights': true,
      },
      fcmTokens: {},
    );
  }

  /// From JSON factory
  factory NotificationPreferences.fromJson(Map<String, dynamic> json) =>
      _$NotificationPreferencesFromJson(json);

  /// To JSON method
  Map<String, dynamic> toJson() => _$NotificationPreferencesToJson(this);

  /// Copy with method for updating notification preferences
  NotificationPreferences copyWith({
    bool? enablePushNotifications,
    bool? enableEmailNotifications,
    Map<String, bool>? topicPreferences,
    Map<String, String>? fcmTokens,
  }) {
    return NotificationPreferences(
      enablePushNotifications:
          enablePushNotifications ?? this.enablePushNotifications,
      enableEmailNotifications:
          enableEmailNotifications ?? this.enableEmailNotifications,
      topicPreferences: topicPreferences ?? this.topicPreferences,
      fcmTokens: fcmTokens ?? this.fcmTokens,
    );
  }

  /// Check if a specific topic is enabled
  bool isTopicEnabled(String topic) {
    return topicPreferences[topic] ?? false;
  }

  /// Enable or disable a specific topic
  NotificationPreferences setTopicEnabled(String topic, bool enabled) {
    final updatedPreferences = Map<String, bool>.from(topicPreferences);
    updatedPreferences[topic] = enabled;
    return copyWith(topicPreferences: updatedPreferences);
  }

  /// Add or update FCM token for a specific device
  NotificationPreferences setDeviceToken(String deviceId, String token) {
    final updatedTokens = Map<String, String>.from(fcmTokens);
    updatedTokens[deviceId] = token;
    return copyWith(fcmTokens: updatedTokens);
  }

  /// Remove FCM token for a specific device
  NotificationPreferences removeDeviceToken(String deviceId) {
    final updatedTokens = Map<String, String>.from(fcmTokens);
    updatedTokens.remove(deviceId);
    return copyWith(fcmTokens: updatedTokens);
  }

  /// Get FCM token for a specific device
  String? getDeviceToken(String deviceId) {
    return fcmTokens[deviceId];
  }

  /// Check if a device has an FCM token
  bool hasDeviceToken(String deviceId) {
    return fcmTokens.containsKey(deviceId) &&
        fcmTokens[deviceId]?.isNotEmpty == true;
  }

  /// Get all device IDs with FCM tokens
  List<String> get deviceIds => fcmTokens.keys.toList();

  /// Get all FCM tokens
  List<String> get allTokens => fcmTokens.values.toList();
}

@JsonSerializable()
class User {
  final String id;
  final String? name;
  final String email;
  final bool isOnboarded;
  final bool isAdmin;
  final String? description;
  final List<String> preferredPersonaIds;
  final NotificationPreferences? notificationPreferences;

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  User({
    required this.id,
    this.name,
    required this.email,
    this.isOnboarded = false,
    this.isAdmin = false,
    this.description,
    this.preferredPersonaIds = const [],
    this.notificationPreferences,
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);

  // Copy with method for updating user data
  User copyWith({
    String? id,
    String? name,
    String? email,
    bool? isOnboarded,
    bool? isAdmin,
    String? description,
    List<String>? preferredPersonaIds,
    NotificationPreferences? notificationPreferences,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      isOnboarded: isOnboarded ?? this.isOnboarded,
      isAdmin: isAdmin ?? this.isAdmin,
      description: description ?? this.description,
      preferredPersonaIds: preferredPersonaIds ?? this.preferredPersonaIds,
      notificationPreferences:
          notificationPreferences ?? this.notificationPreferences,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class Chat {
  final String? id; // Firestore document ID
  final String title;
  final String ownerId; // User uid
  final String systemPersonaId; // Associated system persona (now required)

  @TimestampDateTimeConverter()
  final DateTime startedDate;

  @TimestampDateTimeConverter()
  final DateTime lastUpdatedDate;

  final bool isCompleted;
  final bool? archived;
  final String? pathStepId; // Link to specific path step
  final bool? isTitleLocked; // Prevent automatic title generation when true
  final Map<String, dynamic>? metadata;

  Chat({
    this.id,
    required this.title,
    required this.ownerId,
    required this.systemPersonaId,
    required this.startedDate,
    required this.lastUpdatedDate,
    required this.isCompleted,
    this.archived,
    this.pathStepId,
    this.isTitleLocked,
    this.metadata,
  });

  // From JSON factory
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Message {
  final String? id; // Firestore document ID
  final String chatId;
  final String chatOwnerId; // User uid from Chat entity for efficient rules
  final String? userId; // Message author (Firebase userId)
  final String? systemPersonaId;

  @TimestampDateTimeConverter()
  final DateTime postedDate;

  final bool isDeleted;
  final String type;
  final String? textContent;
  final String? imageUrl;
  final Map<String, int> reactionCounts;
  final bool? edited;

  @NullableTimestampDateTimeConverter()
  final DateTime? editDate;

  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;

  Message({
    this.id,
    required this.chatId,
    required this.chatOwnerId,
    required this.userId,
    this.systemPersonaId,
    required this.postedDate,
    required this.isDeleted,
    required this.type,
    this.textContent,
    this.imageUrl,
    required this.reactionCounts,
    this.edited,
    this.editDate,
    this.replyToMessageId,
    this.metadata,
  });

  // From JSON factory
  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$MessageToJson(this);
}

@JsonSerializable()
class SystemPersona {
  final String? id; // Firestore document ID
  final String name;
  final String title;
  final String avatarUrl;
  final String description;
  final bool isActive;
  final String approach;
  final String coachingStyle;
  final List<String> specialties;
  final String videoUrl;
  final String catchphrase;

  SystemPersona({
    this.id,
    required this.name,
    required this.title,
    required this.avatarUrl,
    required this.description,
    required this.isActive,
    required this.approach,
    required this.coachingStyle,
    required this.specialties,
    required this.videoUrl,
    required this.catchphrase,
  });

  // From JSON factory
  factory SystemPersona.fromJson(Map<String, dynamic> json) =>
      _$SystemPersonaFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$SystemPersonaToJson(this);
}

@JsonSerializable()
class RelationInfo {
  final String name;
  final int age;
  final String relation; // e.g. 'son', 'spouse', 'dog', etc.
  final List<String>? otherInfo; // Additional notes about this relation

  RelationInfo({
    required this.name,
    required this.age,
    required this.relation,
    this.otherInfo,
  });

  // From JSON factory
  factory RelationInfo.fromJson(Map<String, dynamic> json) =>
      _$RelationInfoFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$RelationInfoToJson(this);

  // Copy with method
  RelationInfo copyWith({
    String? name,
    int? age,
    String? relation,
    List<String>? otherInfo,
  }) {
    return RelationInfo(
      name: name ?? this.name,
      age: age ?? this.age,
      relation: relation ?? this.relation,
      otherInfo: otherInfo ?? this.otherInfo,
    );
  }
}

@JsonSerializable()
class Location {
  final String? town;
  final String country;

  Location({this.town, required this.country});

  // From JSON factory
  factory Location.fromJson(Map<String, dynamic> json) =>
      _$LocationFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$LocationToJson(this);

  // Copy with method
  Location copyWith({String? town, String? country}) {
    return Location(town: town ?? this.town, country: country ?? this.country);
  }
}

@JsonSerializable()
class Fact {
  final String key;
  final dynamic value; // Can be string, number, or boolean

  Fact({required this.key, required this.value});

  // From JSON factory
  factory Fact.fromJson(Map<String, dynamic> json) => _$FactFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$FactToJson(this);

  // Copy with method
  Fact copyWith({String? key, dynamic value}) {
    return Fact(key: key ?? this.key, value: value ?? this.value);
  }
}

@JsonSerializable()
class Goal {
  final String id;
  final String description;
  final String status; // 'not_started', 'in_progress', 'achieved', 'abandoned'

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  @NullableTimestampDateTimeConverter()
  final DateTime? updatedAt;

  Goal({
    required this.id,
    required this.description,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  // From JSON factory
  factory Goal.fromJson(Map<String, dynamic> json) => _$GoalFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$GoalToJson(this);

  // Copy with method
  Goal copyWith({
    String? id,
    String? description,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Goal(
      id: id ?? this.id,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class InteractionSource {
  final String sessionId;

  @TimestampDateTimeConverter()
  final DateTime timestamp;

  InteractionSource({required this.sessionId, required this.timestamp});

  // From JSON factory
  factory InteractionSource.fromJson(Map<String, dynamic> json) =>
      _$InteractionSourceFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$InteractionSourceToJson(this);

  // Copy with method
  InteractionSource copyWith({String? sessionId, DateTime? timestamp}) {
    return InteractionSource(
      sessionId: sessionId ?? this.sessionId,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class UserProfile {
  final String userId;
  final String? name;
  final int? age;
  final String?
  gender; // 'male', 'female', 'non-binary', 'other', 'unspecified'
  final String?
  familyStatus; // 'single', 'married', 'partnered', 'divorced', 'widowed', 'unspecified'
  final List<RelationInfo>? family;
  final Location? location;
  final List<Fact>? facts;
  final List<String>? likes;
  final List<String>? dislikes;
  final Map<String, dynamic>? preferences;
  final List<Goal>? goals;
  final List<String>? personalityTraits;
  final InteractionHistory interactionHistory;

  UserProfile({
    required this.userId,
    this.name,
    this.age,
    this.gender,
    this.familyStatus,
    this.family,
    this.location,
    this.facts,
    this.likes,
    this.dislikes,
    this.preferences,
    this.goals,
    this.personalityTraits,
    required this.interactionHistory,
  });

  // From JSON factory
  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  // Copy with method
  UserProfile copyWith({
    String? userId,
    String? name,
    int? age,
    String? gender,
    String? familyStatus,
    List<RelationInfo>? family,
    Location? location,
    List<Fact>? facts,
    List<String>? likes,
    List<String>? dislikes,
    Map<String, dynamic>? preferences,
    List<Goal>? goals,
    List<String>? personalityTraits,
    InteractionHistory? interactionHistory,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      familyStatus: familyStatus ?? this.familyStatus,
      family: family ?? this.family,
      location: location ?? this.location,
      facts: facts ?? this.facts,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      preferences: preferences ?? this.preferences,
      goals: goals ?? this.goals,
      personalityTraits: personalityTraits ?? this.personalityTraits,
      interactionHistory: interactionHistory ?? this.interactionHistory,
    );
  }

  /// Returns a copy of this UserProfile with all fact values normalized to strings
  /// This ensures consistent data types when saving to Firestore
  UserProfile withNormalizedFactValues() {
    if (facts == null || facts!.isEmpty) {
      return this;
    }

    final normalizedFacts = facts!.map((fact) {
      return Fact(key: fact.key, value: _normalizeFactValue(fact.value));
    }).toList();

    return copyWith(facts: normalizedFacts);
  }

  /// Converts a fact value to a string representation for consistent storage
  static String _normalizeFactValue(dynamic value) {
    if (value == null) {
      return '';
    }

    if (value is String) {
      return value;
    }

    if (value is int || value is double) {
      return value.toString();
    }

    if (value is bool) {
      return value.toString();
    }

    // For any other type, convert to string
    return value.toString();
  }
}

@JsonSerializable(explicitToJson: true)
class InteractionHistory {
  @TimestampDateTimeConverter()
  final DateTime lastUpdated;

  final List<InteractionSource>? sources;

  InteractionHistory({required this.lastUpdated, this.sources});

  // From JSON factory
  factory InteractionHistory.fromJson(Map<String, dynamic> json) =>
      _$InteractionHistoryFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$InteractionHistoryToJson(this);

  // Copy with method
  InteractionHistory copyWith({
    DateTime? lastUpdated,
    List<InteractionSource>? sources,
  }) {
    return InteractionHistory(
      lastUpdated: lastUpdated ?? this.lastUpdated,
      sources: sources ?? this.sources,
    );
  }
}

@JsonSerializable()
class GuidedPath {
  final String? id; // Firestore document ID
  final String name;
  final String category; // 'Focus & Productivity', 'Mindset & Resilience'
  final String description;
  final int stepCount;
  final String targetUserTier; // 'free', 'paid'
  final String? imageUrl;
  final int? estimatedCompletionTimeMinutes;
  final String? difficultyLevel; // 'beginner', 'intermediate', 'advanced'
  final List<String>? prerequisites;
  final bool isActive;

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  @NullableTimestampDateTimeConverter()
  final DateTime? updatedAt;

  final Map<String, dynamic>? metadata;

  GuidedPath({
    this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.stepCount,
    required this.targetUserTier,
    this.imageUrl,
    this.estimatedCompletionTimeMinutes,
    this.difficultyLevel,
    this.prerequisites,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  // From JSON factory
  factory GuidedPath.fromJson(Map<String, dynamic> json) =>
      _$GuidedPathFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$GuidedPathToJson(this);

  // Copy with method
  GuidedPath copyWith({
    String? id,
    String? name,
    String? category,
    String? description,
    int? stepCount,
    String? targetUserTier,
    String? imageUrl,
    int? estimatedCompletionTimeMinutes,
    String? difficultyLevel,
    List<String>? prerequisites,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return GuidedPath(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      stepCount: stepCount ?? this.stepCount,
      targetUserTier: targetUserTier ?? this.targetUserTier,
      imageUrl: imageUrl ?? this.imageUrl,
      estimatedCompletionTimeMinutes:
          estimatedCompletionTimeMinutes ?? this.estimatedCompletionTimeMinutes,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      prerequisites: prerequisites ?? this.prerequisites,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// External resource types for guided path steps
enum ExternalResourceType {
  @JsonValue('article')
  article,
  @JsonValue('video')
  video,
  @JsonValue('podcast')
  podcast,
  @JsonValue('tool')
  tool,
  @JsonValue('book')
  book,
  @JsonValue('course')
  course,
  @JsonValue('website')
  website,
  @JsonValue('other')
  other,
}

/// Extension methods for ExternalResourceType
extension ExternalResourceTypeExtension on ExternalResourceType {
  /// Get the string identifier for this resource type
  String get identifier {
    switch (this) {
      case ExternalResourceType.article:
        return 'article';
      case ExternalResourceType.video:
        return 'video';
      case ExternalResourceType.podcast:
        return 'podcast';
      case ExternalResourceType.tool:
        return 'tool';
      case ExternalResourceType.book:
        return 'book';
      case ExternalResourceType.course:
        return 'course';
      case ExternalResourceType.website:
        return 'website';
      case ExternalResourceType.other:
        return 'other';
    }
  }

  /// Get the display name for this resource type
  String get displayName {
    switch (this) {
      case ExternalResourceType.article:
        return 'Article';
      case ExternalResourceType.video:
        return 'Video';
      case ExternalResourceType.podcast:
        return 'Podcast';
      case ExternalResourceType.tool:
        return 'Tool';
      case ExternalResourceType.book:
        return 'Book';
      case ExternalResourceType.course:
        return 'Course';
      case ExternalResourceType.website:
        return 'Website';
      case ExternalResourceType.other:
        return 'Other';
    }
  }
}

/// External resource for guided path steps
@JsonSerializable()
class ExternalResource {
  final String title;
  final String link; // URL to the resource
  final ExternalResourceType type;
  final String? description;
  final String? source; // Source attribution (e.g., "YouTube", "Medium")
  final int? durationMinutes; // For videos, podcasts, courses
  final Map<String, dynamic>? metadata; // Additional resource metadata

  ExternalResource({
    required this.title,
    required this.link,
    required this.type,
    this.description,
    this.source,
    this.durationMinutes,
    this.metadata,
  });

  // From JSON factory
  factory ExternalResource.fromJson(Map<String, dynamic> json) =>
      _$ExternalResourceFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ExternalResourceToJson(this);

  // Copy with method
  ExternalResource copyWith({
    String? title,
    String? link,
    ExternalResourceType? type,
    String? description,
    String? source,
    int? durationMinutes,
    Map<String, dynamic>? metadata,
  }) {
    return ExternalResource(
      title: title ?? this.title,
      link: link ?? this.link,
      type: type ?? this.type,
      description: description ?? this.description,
      source: source ?? this.source,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class PathStep {
  final String? id; // Firestore document ID
  final String pathId; // Reference to the GuidedPath
  final int stepNumber; // Order/sequence of the step (1-based)
  final String title;
  final String description;
  final String completionCriteria;
  final int? estimatedDurationMinutes;
  final List<ExternalResource>? resources; // Structured resource objects
  final List<String>? reflectionPrompts;
  final bool isActive;

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  @NullableTimestampDateTimeConverter()
  final DateTime? updatedAt;

  final Map<String, dynamic>? metadata;

  PathStep({
    this.id,
    required this.pathId,
    required this.stepNumber,
    required this.title,
    required this.description,
    required this.completionCriteria,
    this.estimatedDurationMinutes,
    this.resources,
    this.reflectionPrompts,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  // From JSON factory
  factory PathStep.fromJson(Map<String, dynamic> json) =>
      _$PathStepFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$PathStepToJson(this);

  // Copy with method
  PathStep copyWith({
    String? id,
    String? pathId,
    int? stepNumber,
    String? title,
    String? description,
    String? completionCriteria,
    int? estimatedDurationMinutes,
    List<ExternalResource>? resources,
    List<String>? reflectionPrompts,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PathStep(
      id: id ?? this.id,
      pathId: pathId ?? this.pathId,
      stepNumber: stepNumber ?? this.stepNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      completionCriteria: completionCriteria ?? this.completionCriteria,
      estimatedDurationMinutes:
          estimatedDurationMinutes ?? this.estimatedDurationMinutes,
      resources: resources ?? this.resources,
      reflectionPrompts: reflectionPrompts ?? this.reflectionPrompts,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class PathStepProgress {
  final String stepId; // Step identifier (Firestore id)
  final String? chatId; // Associated chat ID for this step
  final bool isCompleted; // Whether the step is completed

  @NullableTimestampDateTimeConverter()
  final DateTime? startedDate;

  @NullableTimestampDateTimeConverter()
  final DateTime? completedDate;

  PathStepProgress({
    required this.stepId,
    this.chatId,
    required this.isCompleted,
    this.startedDate,
    this.completedDate,
  });

  // From JSON factory
  factory PathStepProgress.fromJson(Map<String, dynamic> json) =>
      _$PathStepProgressFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$PathStepProgressToJson(this);

  // Copy with method
  PathStepProgress copyWith({
    String? stepId,
    String? chatId,
    bool? isCompleted,
    DateTime? startedDate,
    DateTime? completedDate,
  }) {
    return PathStepProgress(
      stepId: stepId ?? this.stepId,
      chatId: chatId ?? this.chatId,
      isCompleted: isCompleted ?? this.isCompleted,
      startedDate: startedDate ?? this.startedDate,
      completedDate: completedDate ?? this.completedDate,
    );
  }

  // Helper methods
  bool get hasChatId => chatId != null && chatId!.isNotEmpty;
}

@JsonSerializable(explicitToJson: true)
class UserPathProgress {
  final String? id; // Firestore document ID
  final String userId; // Firebase user ID
  final String pathId; // Reference to the GuidedPath
  final bool isCompleted; // Whether the path is completed

  @TimestampDateTimeConverter()
  final DateTime startedDate;

  @TimestampDateTimeConverter()
  final DateTime lastAccessedDate;

  @NullableTimestampDateTimeConverter()
  final DateTime? completionDate;

  final Map<String, PathStepProgress>? stepProgress;

  UserPathProgress({
    this.id,
    required this.userId,
    required this.pathId,
    required this.isCompleted,
    required this.startedDate,
    required this.lastAccessedDate,
    this.completionDate,
    this.stepProgress,
  });

  // From JSON factory
  factory UserPathProgress.fromJson(Map<String, dynamic> json) =>
      _$UserPathProgressFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserPathProgressToJson(this);

  // Copy with method
  UserPathProgress copyWith({
    String? id,
    String? userId,
    String? pathId,
    bool? isCompleted,
    DateTime? startedDate,
    DateTime? lastAccessedDate,
    DateTime? completionDate,
    Map<String, PathStepProgress>? stepProgress,
  }) {
    return UserPathProgress(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      pathId: pathId ?? this.pathId,
      isCompleted: isCompleted ?? this.isCompleted,
      startedDate: startedDate ?? this.startedDate,
      lastAccessedDate: lastAccessedDate ?? this.lastAccessedDate,
      completionDate: completionDate ?? this.completionDate,
      stepProgress: stepProgress ?? this.stepProgress,
    );
  }

  double getProgressPercentage(int totalSteps) {
    if (totalSteps == 0) return 0.0;
    final completedCount = getCompletedStepsCount();
    return completedCount / totalSteps;
  }

  bool isStepCompleted(String stepId) {
    // Check if the specific step is completed
    return stepProgress?[stepId]?.isCompleted ?? false;
  }

  // Get count of completed steps from stepProgress map
  int getCompletedStepsCount() {
    return stepProgress?.values
            .where((progress) => progress.isCompleted)
            .length ??
        0;
  }

  // Step progress helper methods
  PathStepProgress? getStepProgress(String stepId) {
    return stepProgress?[stepId];
  }

  String? getStepChatId(String stepId) {
    return getStepProgress(stepId)?.chatId;
  }

  bool hasStepChatId(String stepId) {
    final progress = getStepProgress(stepId);
    return progress?.hasChatId ?? false;
  }

  bool getStepStatus(String stepId) {
    return getStepProgress(stepId)?.isCompleted ?? false;
  }

  // Update step progress
  UserPathProgress updateStepProgress(
    String stepId,
    PathStepProgress newProgress,
  ) {
    final updatedStepProgress = Map<String, PathStepProgress>.from(
      stepProgress ?? {},
    );
    updatedStepProgress[stepId] = newProgress;

    return copyWith(
      stepProgress: updatedStepProgress,
      lastAccessedDate: DateTime.now(),
    );
  }

  // Set chat ID for a step
  UserPathProgress setStepChatId(String stepId, String chatId) {
    final currentProgress = getStepProgress(stepId);
    final newProgress =
        (currentProgress ??
                PathStepProgress(stepId: stepId, isCompleted: false))
            .copyWith(
              chatId: chatId,
              isCompleted: false, // Step is in progress when chat is started
              startedDate: currentProgress?.startedDate ?? DateTime.now(),
            );

    return updateStepProgress(stepId, newProgress);
  }

  // Mark step as completed
  UserPathProgress markStepCompleted(String stepId) {
    final currentProgress = getStepProgress(stepId);
    final newProgress =
        (currentProgress ??
                PathStepProgress(stepId: stepId, isCompleted: false))
            .copyWith(isCompleted: true, completedDate: DateTime.now());

    final updatedProgress = updateStepProgress(stepId, newProgress);

    // Update overall completion status based on all steps
    final allStepsCompleted = _areAllStepsCompleted();

    return updatedProgress.copyWith(
      isCompleted: allStepsCompleted,
      completionDate: allStepsCompleted ? DateTime.now() : null,
    );
  }

  // Check if all steps are completed
  bool _areAllStepsCompleted() {
    if (stepProgress == null || stepProgress!.isEmpty) {
      return false;
    }

    return stepProgress!.values.every((progress) => progress.isCompleted);
  }
}
